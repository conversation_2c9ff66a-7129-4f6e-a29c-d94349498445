/**
 * 火山引擎统计分析客户端插件
 * 配置火山引擎统计参数
 */

export default defineNuxtPlugin(() => {
  // 只在客户端运行，如果没有配置火山ID则跳过
  if (!import.meta.client || !process.env.NUXT_PUBLIC_VOLC_APP_ID) return

  const config = useRuntimeConfig()

  // 火山引擎配置
  const volcConfig = {
    // 从环境变量获取APP ID，确保转换为数字类型
    app_id: Number(config.public.volcAppId) ||
      (config.public.appName?.toLowerCase().includes('reel')
        ? 20005873
        : 20008376),
    channel: 'web',
    log: false, // 日志
    disable_sdk_monitor: false,
    disable_auto_pv: true, // 禁用自动页面浏览统计，手动控制
  }

  // 等待火山引擎主脚本加载完成并配置
  const configVolcAnalytics = () => {
    if (typeof window !== 'undefined' && (window as any).collectEvent) {
      // 检查是否是真正的collectEvent函数（不是缓存队列）
      if (
        typeof (window as any).collectEvent === 'function' &&
        !(window as any).collectEvent.q
      ) {
        console.log('🌋 火山引擎主脚本已加载')

        // 初始化火山引擎（使用与CSR应用相同的配置）
        try {
          ;(window as any).collectEvent('init', {
            app_id: volcConfig.app_id,
            channel_domain: 'https://gator.volces.com', // 火山引擎上报服务器
            log: volcConfig.log,
            auto_track: true, // 启用自动追踪
            disable_sdk_monitor: false,
            enable_stay_duration: true, // 启用停留时长统计
            spa: true, // 单页应用模式
            channel: volcConfig.channel,
            disable_auto_pv: volcConfig.disable_auto_pv,
          })
          if (process.env.NODE_ENV === 'development') {
            console.log('🌋 火山引擎初始化成功', volcConfig)
          }
          window.collectEvent!('start')
          return true
        } catch (error) {
          console.error('🌋 火山引擎初始化失败:', error)
          return false
        }
      } else {
        if (process.env.NODE_ENV === 'development') {
          console.log('🌋 火山引擎缓存队列已就绪，等待主脚本加载...')
        }
        return false
      }
    } else {
      if (process.env.NODE_ENV === 'development') {
        console.log('🌋 等待火山引擎脚本加载...')
      }
      return false
    }
  }

  // 轮询检查火山引擎是否加载完成
  let isConfigured = false
  const checkInterval = setInterval(() => {
    if (configVolcAnalytics()) {
      isConfigured = true
      clearInterval(checkInterval)
    }
  }, 200)

  // 5秒后停止检查
  setTimeout(() => {
    clearInterval(checkInterval)
    if (process.env.NODE_ENV === 'development' && !isConfigured) {
      console.warn('🔧 火山引擎加载检查超时，可能需要检查网络连接')
    }
  }, 5000)
})

// 类型声明
declare global {
  interface Window {
    collectEvent?: (eventType: string, eventData?: any) => void
  }
}
