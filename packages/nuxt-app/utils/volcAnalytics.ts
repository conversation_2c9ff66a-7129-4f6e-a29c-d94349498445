/**
 * 火山引擎统计分析工具 - 简化版
 * 只在 ReelPlay 环境下提供事件上报，直接调用 window.collectEvent
 */

// 类型定义
export interface VolcEventData {
  [key: string]: any
}

// 全局声明
declare global {
  interface Window {
    collectEvent?: (eventType: string, eventData?: any) => void
  }
}

/**
 * 简化的火山引擎事件上报
 * 直接调用 window.collectEvent，不需要复杂的等待逻辑
 */
export function trackVolcEvent(
  eventType: string,
  eventData?: VolcEventData,
): boolean {
  if (!import.meta.client) {
    return false
  }

  // 检查是否在 ReelPlay 环境
  const config = useRuntimeConfig()
  const isReelPlay = config.public.appName?.toLowerCase().includes('reel')
  if (!isReelPlay) {
    return false
  }

  // 检查火山引擎是否可用
  if (!window.collectEvent) {
    if (process.env.NODE_ENV === 'development') {
      console.warn('[Volc Analytics] 火山引擎未加载')
    }
    return false
  }

  try {
    // 添加通用数据
    const enrichedData = {
      ...eventData,
      timestamp: Date.now(),
      page_url: window.location.href,
      page_path: window.location.pathname,
    }

    window.collectEvent(eventType, enrichedData)

    if (process.env.NODE_ENV === 'development') {
      console.log('[Volc Analytics] 事件上报:', eventType, enrichedData)
    }

    return true
  } catch (error) {
    console.error('[Volc Analytics] 事件上报失败:', error)
    return false
  }
}

/**
 * 页面浏览上报
 */
export function trackVolcPageView(path?: string): void {
  if (!import.meta.client) return

  const pagePath = path || window.location.pathname

  trackVolcEvent('PageView', {
    page_path: pagePath,
    page_title: document.title,
    referrer: document.referrer || 'direct',
  })
}

/**
 * 用户行为上报
 */
export function trackVolcUserAction(
  action: string,
  details?: VolcEventData,
): void {
  trackVolcEvent('UserAction', {
    action,
    ...details,
  })
}

/**
 * 检查火山引擎是否可用
 */
export function isVolcAvailable(): boolean {
  if (!import.meta.client) return false

  const config = useRuntimeConfig()
  const isReelPlay = config.public.appName?.toLowerCase().includes('reel')

  return isReelPlay && !!window.collectEvent
}

/**
 * 开发环境调试函数
 */
export function debugVolcAnalytics(): void {
  if (process.env.NODE_ENV !== 'development') {
    console.warn('调试函数只在开发环境可用')
    return
  }

  const config = useRuntimeConfig()
  const isReelPlay = config.public.appName?.toLowerCase().includes('reel')

  console.group('🌋 火山引擎调试信息')
  console.log('是否 ReelPlay 环境:', isReelPlay)
  console.log('window.collectEvent 存在:', !!window.collectEvent)
  console.log('当前页面:', window.location.href)

  if (!isReelPlay) {
    console.log('⚠️ 非 ReelPlay 环境，火山引擎已禁用')
  } else if (window.collectEvent) {
    console.log('✅ 火山引擎可用')
    // 发送测试事件
    trackVolcEvent('debug_test', {
      test: true,
      debug_time: new Date().toISOString(),
    })
    console.log('✅ 测试事件已发送')
  } else {
    console.warn('❌ 火山引擎脚本未加载')
  }

  console.groupEnd()
}

// 开发环境自动添加到全局
if (process.env.NODE_ENV === 'development' && import.meta.client) {
  setTimeout(() => {
    ;(window as any).debugVolcAnalytics = debugVolcAnalytics
    ;(window as any).trackVolcEvent = trackVolcEvent
    console.log('🔧 火山引擎调试工具已加载，使用 debugVolcAnalytics() 进行调试')
  }, 1000)
}
