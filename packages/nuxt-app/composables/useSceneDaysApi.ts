import type { SceneDaysRequest, SceneDaysResponse } from '~/types/chat4-time'

/**
 * 场景天数API相关功能
 */
export const useSceneDaysApi = () => {
  const { apiRequest } = useApi()

  /**
   * 获取游戏进行天数和场景访问记录
   */
  const fetchSceneDays = async (
    storyId: string, 
    actorId: string
  ): Promise<SceneDaysResponse> => {
    const body: SceneDaysRequest = {
      story_id: storyId,
      actor_id: actorId
    }

    try {
      const response = await apiRequest<SceneDaysResponse['data']>(
        '/api/v1/game/scene-days.get',
        {
          method: 'POST',
          body,
          requireAuth: true
        }
      )

      return {
        code: response.code,
        message: response.message,
        data: response.data
      }
    } catch (error) {
      console.error('[SceneDaysAPI] 获取场景天数失败:', error)
      
      // 返回错误响应
      return {
        code: 'ERROR',
        message: error instanceof Error ? error.message : '获取场景天数失败',
        data: {
          days: 1,
          scene_ids: []
        }
      }
    }
  }

  /**
   * 批量获取多个游戏的场景天数（如果需要）
   */
  const fetchMultipleSceneDays = async (
    requests: { storyId: string; actorId: string }[]
  ): Promise<Map<string, SceneDaysResponse>> => {
    const results = new Map<string, SceneDaysResponse>()

    // 并发请求所有数据
    const promises = requests.map(async ({ storyId, actorId }) => {
      const gameKey = `${storyId}:${actorId}`
      try {
        const response = await fetchSceneDays(storyId, actorId)
        results.set(gameKey, response)
      } catch (error) {
        console.error(`[SceneDaysAPI] 获取${gameKey}场景天数失败:`, error)
        // 设置默认值
        results.set(gameKey, {
          code: 'ERROR',
          message: '获取失败',
          data: { days: 1, scene_ids: [] }
        })
      }
    })

    await Promise.allSettled(promises)
    return results
  }

  return {
    fetchSceneDays,
    fetchMultipleSceneDays
  }
}