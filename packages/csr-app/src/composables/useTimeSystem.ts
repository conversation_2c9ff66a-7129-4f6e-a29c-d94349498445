import { ref, computed } from 'vue'
import { useApi } from './useApi'
import axios from 'axios'

/**
 * Chat4时间系统的简化版本
 * 用于CSR应用中的场景天数管理
 */

interface SceneDaysResponse {
  code: string
  message: string
  data: {
    days: number
    scene_ids: string[]
  }
}

interface GameTimeState {
  currentDay: number
  visitedScenes: string[]
  lastUpdateTime: number
  isValid: boolean
}

// 全局状态缓存
const gameStatesCache = new Map<string, GameTimeState>()

export const useTimeSystem = (storyId: string, actorId: string) => {
  const { withLoading, handleApiResponse } = useApi()
  const gameKey = `${storyId}:${actorId}`

  // ========== 响应式状态 ==========

  const isLoading = ref(false)
  const currentDay = ref(1)
  const visitedScenes = ref<string[]>([])

  // ========== 计算属性 ==========

  const isDataValid = computed(() => {
    const cached = gameStatesCache.get(gameKey)
    if (!cached) return false
    
    const now = Date.now()
    const elapsed = now - cached.lastUpdateTime
    return elapsed < 5 * 60 * 1000 && cached.isValid // 5分钟缓存
  })

  // ========== 核心方法 ==========

  /**
   * 获取场景天数数据
   */
  const fetchSceneDays = async (): Promise<SceneDaysResponse> => {
    try {
      const response = await withLoading(async () => {
        // 使用 axios 调用后端接口，自动支持请求取消机制
        const res = await axios.post('/api/v1/game/scene-days.get', {
          story_id: storyId,
          actor_id: actorId
        })
        
        return handleApiResponse(res.data)
      })

      return {
        code: '0',
        message: 'success',
        data: response || { days: 1, scene_ids: [] }
      }
    } catch (error) {
      // 检查是否是请求被取消
      if (axios.isCancel(error)) {
        console.log('[TimeSystem] 请求被取消:', error.message)
        return {
          code: 'CANCELLED',
          message: '请求被取消',
          data: { days: 1, scene_ids: [] }
        }
      }
      
      console.error('[TimeSystem] 获取场景天数失败:', error)
      
      return {
        code: 'ERROR',
        message: error instanceof Error ? error.message : '获取场景天数失败',
        data: { days: 1, scene_ids: [] }
      }
    }
  }

  /**
   * 刷新时间数据
   */
  const refreshTimeData = async (): Promise<{
    success: boolean
    data?: { days: number; scene_ids: string[] }
    error?: string
  }> => {
    // 如果数据仍然有效，直接返回缓存
    if (isDataValid.value) {
      const cached = gameStatesCache.get(gameKey)
      if (cached) {
        return {
          success: true,
          data: {
            days: cached.currentDay,
            scene_ids: cached.visitedScenes
          }
        }
      }
    }

    isLoading.value = true

    try {
      // 从API获取最新数据
      const response = await fetchSceneDays()
      
      if (response.code === '0' && response.data) {
        // 更新响应式状态
        currentDay.value = response.data.days
        visitedScenes.value = [...response.data.scene_ids]
        
        // 更新缓存
        gameStatesCache.set(gameKey, {
          currentDay: response.data.days,
          visitedScenes: [...response.data.scene_ids],
          lastUpdateTime: Date.now(),
          isValid: true
        })
        
        isLoading.value = false
        
        return {
          success: true,
          data: response.data
        }
      } else {
        isLoading.value = false
        return {
          success: false,
          error: response.message || '获取时间数据失败'
        }
      }
    } catch (error) {
      isLoading.value = false
      const errorMessage = error instanceof Error ? error.message : '网络错误'
      
      console.error('[TimeSystem] 刷新时间数据失败:', error)
      
      return {
        success: false,
        error: errorMessage
      }
    }
  }

  /**
   * 检查场景是否为重复访问
   */
  const checkSceneRevisit = (sceneId: string): boolean => {
    return visitedScenes.value.includes(sceneId)
  }

  /**
   * 获取场景访问统计
   */
  const getSceneStats = () => {
    const totalScenes = visitedScenes.value.length
    
    return {
      totalScenes,
      currentDay: currentDay.value,
      visitedScenes: visitedScenes.value
    }
  }

  /**
   * 重置天数接口调用
   */
  const resetDays = async (): Promise<{
    success: boolean
    data?: { days: number; scene_ids: string[] }
    error?: string
  }> => {
    try {
      const response = await withLoading(async () => {
        // 调用重置天数接口
        const res = await axios.post('/api/v1/game/scene-days.reset', {
          story_id: storyId,
          actor_id: actorId
        })
        
        return handleApiResponse(res.data)
      })

      // 清除缓存，强制刷新数据
      gameStatesCache.delete(gameKey)
      
      // 重置成功后刷新数据
      const refreshResult = await refreshTimeData()
      
      return {
        success: true,
        data: refreshResult.data
      }
    } catch (error) {
      // 检查是否是请求被取消
      if (axios.isCancel(error)) {
        console.log('[TimeSystem] 重置请求被取消:', error.message)
        return {
          success: false,
          error: '请求被取消'
        }
      }
      
      console.error('[TimeSystem] 重置天数失败:', error)
      
      return {
        success: false,
        error: error instanceof Error ? error.message : '重置天数失败'
      }
    }
  }

  /**
   * 强制刷新数据（忽略缓存）
   */
  const forceRefresh = async () => {
    // 清除缓存
    gameStatesCache.delete(gameKey)
    return await refreshTimeData()
  }

  /**
   * 初始化时间数据
   */
  const initialize = async () => {
    // 尝试从缓存加载
    const cached = gameStatesCache.get(gameKey)
    if (cached && cached.isValid) {
      currentDay.value = cached.currentDay
      visitedScenes.value = [...cached.visitedScenes]
    }

    // 如果数据无效，自动刷新
    if (!isDataValid.value) {
      await refreshTimeData()
    }
  }

  // 自动初始化
  initialize()

  // ========== 返回接口 ==========

  return {
    // 响应式状态
    currentDay,
    visitedScenes,
    isLoading,
    isDataValid,
    
    // 核心方法
    refreshTimeData,
    checkSceneRevisit,
    getSceneStats,
    forceRefresh,
    initialize,
    
    // API方法
    fetchSceneDays,
    resetDays
  }
}