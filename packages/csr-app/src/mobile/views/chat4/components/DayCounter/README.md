# DayCounter 组件使用指南

## 🎯 组件概述

DayCounter 是 Chat4 时间系统的核心UI组件，提供天数显示和"Next Day"功能。组件完美复现了设计图中的蓝色虚线边框样式和天数重置功能。

## 📸 设计参考

组件基于设计图实现：
- 左侧蓝色虚线框：显示"Get Along: X Days"
- 右侧紫色按钮：点击触发天数重置

## 🚀 快速使用

### 基础用法

```vue
<template>
  <DayCounter
    :story-id="storyId"
    :actor-id="actorId"
    @next-day="handleNextDay"
    @day-reset="handleDayReset"
  />
</template>

<script setup>
import DayCounter from '../DayCounter/index.vue'

const handleNextDay = () => {
  console.log('Next Day clicked')
}

const handleDayReset = (newDay) => {
  console.log('Day reset to:', newDay)
}
</script>
```

### 主题样式

组件提供三种预设主题：

```vue
<!-- 默认主题（蓝色） -->
<DayCounter :story-id="storyId" :actor-id="actorId" />

<!-- 地图场景主题（绿色） -->
<DayCounter 
  :story-id="storyId" 
  :actor-id="actorId" 
  theme="map" 
/>

<!-- Phone场景主题（粉色） -->
<DayCounter 
  :story-id="storyId" 
  :actor-id="actorId" 
  theme="phone" 
/>
```

## 📋 Props 参数

| 参数 | 类型 | 默认值 | 必需 | 说明 |
|------|------|--------|------|------|
| `story-id` | string | - | ✅ | 故事ID |
| `actor-id` | string | - | ✅ | 角色ID |
| `theme` | 'default' \| 'map' \| 'phone' | 'default' | ❌ | 主题样式 |
| `show-loading` | boolean | true | ❌ | 是否显示加载状态 |

## 📡 Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `next-day` | - | Next Day按钮点击时触发 |
| `day-reset` | `newDay: number` | 天数重置完成时触发 |
| `reset-error` | `error: string` | 重置失败时触发 |

## 🎨 样式定制

组件使用CSS变量，支持主题定制：

```css
.day-counter {
  /* 主要背景渐变 */
  --day-counter-bg: linear-gradient(135deg, rgba(173, 216, 230, 0.9), rgba(135, 206, 235, 0.9));
  
  /* 边框颜色 */
  --day-counter-border: #4a90e2;
  
  /* 按钮背景 */
  --button-bg: linear-gradient(135deg, #8b5cf6, #7c3aed);
}
```

## 🔧 方法调用

组件通过 `defineExpose` 暴露了一些方法：

```vue
<template>
  <DayCounter 
    ref="dayCounterRef"
    :story-id="storyId" 
    :actor-id="actorId" 
  />
</template>

<script setup>
const dayCounterRef = ref()

// 手动刷新天数数据
const refreshData = async () => {
  await dayCounterRef.value?.refresh()
}

// 获取当前天数
const getCurrentDay = () => {
  return dayCounterRef.value?.currentDay
}
</script>
```

## 🏗️ 集成示例

### 在地图场景中使用

```vue
<!-- MapContainer/index.vue -->
<template>
  <div class="map-container">
    <!-- 头部 -->
    <CommonHeader />
    
    <!-- DayCounter 组件 -->
    <div class="day-counter-container">
      <DayCounter
        :story-id="storyStore.currentStory?.id || ''"
        :actor-id="storyStore.currentActor?.id || ''"
        theme="map"
        @next-day="handleNextDay"
        @day-reset="handleDayReset"
      />
    </div>
    
    <!-- 地图内容 -->
    <div class="map-content">
      <!-- ... -->
    </div>
  </div>
</template>

<style>
.day-counter-container {
  position: absolute;
  top: 80px;
  left: 16px;
  right: 16px;
  z-index: 10;
}
</style>
```

### 在Phone场景中使用

```vue
<!-- ChatRoomContainer/index.vue -->
<template>
  <div class="chat-room-container">
    <!-- 背景和头部 -->
    <CommonHeader />
    
    <!-- DayCounter 组件 -->
    <div class="day-counter-container">
      <DayCounter
        :story-id="storyStore.currentStory?.id || ''"
        :actor-id="storyStore.currentActor?.id || ''"
        theme="phone"
        @next-day="handleNextDay"
        @day-reset="handleDayReset"
      />
    </div>
    
    <!-- 聊天消息区域 -->
    <div class="chat-messages">
      <!-- ... -->
    </div>
  </div>
</template>

<style>
.day-counter-container {
  position: absolute;
  top: 80px;
  left: 16px;
  right: 16px;
  z-index: 10;
}

.chat-messages {
  /* 调整top值，为DayCounter预留空间 */
  top: 240px;
}
</style>
```

## 🔄 API集成

组件依赖 `useTimeSystem` composable 获取时间数据：

```typescript
// useTimeSystem.ts
export const useTimeSystem = (storyId: string, actorId: string) => {
  // 调用 /api/v1/game/scene-days.get 接口
  const fetchSceneDays = async () => {
    return await apiRequest('/api/v1/game/scene-days.get', {
      method: 'POST',
      body: { story_id: storyId, actor_id: actorId }
    })
  }
  
  // 返回天数和访问场景数据
  return { currentDay, visitedScenes, refreshTimeData }
}
```

## 🎯 重置API集成

组件已完整集成重置天数的API接口：

```typescript
const handleNextDay = async () => {
  try {
    console.log('[DayCounter] Next Day clicked, calling reset API')
    
    // 调用重置天数接口：POST /api/v1/game/scene-days.reset
    const resetResult = await resetDays()
    
    if (resetResult.success) {
      const newDay = resetResult.data?.days || 1
      emit('day-reset', newDay)
      showSuccessEffect()
    } else {
      throw new Error(resetResult.error || '重置失败')
    }
  } catch (error) {
    emit('reset-error', error.message)
    showErrorEffect()
  }
}
```

**API详细信息**：
- **接口地址**: `POST /api/v1/game/scene-days.reset`
- **请求参数**: 
  - `story_id`: 故事ID
  - `actor_id`: 角色ID
- **功能**: 重置当前故事和角色的天数计数器
- **请求取消**: 自动支持相同请求的取消机制，防止重复提交

## 💡 使用技巧

### 1. 响应式设计
组件已完美适配移动端和桌面端，在小屏幕设备上会自动调整为垂直布局。

### 2. 动画效果
天数变化时会有平滑的数字动画效果，提升用户体验。

### 3. 主题一致性
建议在不同场景中使用对应的主题：
- 地图场景：`theme="map"` (绿色)
- Phone场景：`theme="phone"` (粉色)
- 其他场景：默认主题 (蓝色)

### 4. 错误处理
组件内置完整的错误处理机制，建议监听 `reset-error` 事件显示用户友好的错误提示。

## 🐛 常见问题

### Q: 为什么天数不显示？
A: 请检查 `story-id` 和 `actor-id` 是否正确传入，以及网络连接是否正常。

### Q: Next Day按钮点击无反应？
A: 可能是正在处理中，按钮会显示加载状态。如果持续无反应，请查看控制台错误信息。

### Q: 如何自定义样式？
A: 可以通过CSS变量或者直接覆盖组件样式类来实现自定义。

---

**现在你的Chat4应用已经成功集成了天数管理功能！** 🎉