import { createApp } from 'vue'
import { createHead } from '@unhead/vue'
import router from './router'
import store from './store'
import i18n from './locale'
import AppRoot from './AppRoot.vue'
import { configure } from 'vue-gtag'

// 关键样式优先加载
import '@/assets/style/theme.less'
import '@/assets/style/global.less'

// 关键配置立即加载 - axios 和视口设置
import '@/inject'

// 首屏优化器 - 立即启动
import './utils/smooth-loading' // 丝滑加载器 - 最优先
import './utils/first-screen-performance-optimizer' // 性能优化工具
import './utils/resource-preloader'
import { globalFirstScreenLoading } from './composables/useFirstScreenLoading'
// 导入主应用状态管理
import { setMainAppPageState } from './utils/mainAppState'

// 快速启动应用
const app = createApp(AppRoot)
const head = createHead()

app.use(router)
app.use(store)
app.use(i18n)
app.use(head)

// 配置 Google Analytics (仅在生产环境)
if (
  import.meta.env.MODE === 'prod.playshot' ||
  import.meta.env.MODE === 'prod.reelplay'
) {
  // 根据品牌动态选择 GA ID
  const appName = import.meta.env.VITE_APP_NAME || 'PlayShot'
  const isReelPlay = appName.toLowerCase().includes('reel')
  const gaId = isReelPlay ? 'AW-***********' : 'G-BCD38QPPKH'

  configure({
    tagId: isReelPlay ? 'G-7J3CFG8D6T' : 'G-BCD38QPPKH',
    additionalAccounts: [{ tagId: gaId }],
  })
}

// 立即挂载应用以实现最快首屏
app.mount('#app')

// 标记应用挂载完成，更新首屏加载进度
globalFirstScreenLoading.updateProgress(80)

// 启动注册转换跟踪
setTimeout(() => {
  try {
    import('./composables/useRegistrationTracking')
      .then(({ useRegistrationTracking }) => {
        const { startTracking } = useRegistrationTracking()
        console.log('🎯 启动注册转换跟踪 (vue-gtag-next)...')
        startTracking()
      })
      .catch((error) => {
        console.warn('⚠️ 注册转换跟踪启动失败:', error)
      })
  } catch (error) {
    console.warn('⚠️ 注册转换跟踪模块加载失败:', error)
  }
}, 1500) // 延迟启动，确保用户状态已初始化

// 使用丝滑加载器更新进度
if ((window as any).__smoothLoading) {
  // @ts-ignore
  window.__smoothLoading.setProgress(80)
}

// 通知主应用CSR应用已就绪（在微前端环境中）
setTimeout(() => {
  try {
    // 动态导入避免在非微前端环境中出错
    import('./utils/iframeNavigation')
      .then(({ notifyAppReady, isInMicroFrontend }) => {
        if (isInMicroFrontend()) {
          notifyAppReady()
          console.log('✅ 已通知主应用CSR应用就绪')

          // 在微前端环境中设置消息监听器
          setupParentMessageListener()
        }
      })
      .catch(() => {
        // 忽略导入错误，可能在非微前端环境中
      })
  } catch (error) {
    // 忽略错误，可能在非微前端环境中
  }
}, 500) // 延迟500ms确保应用完全初始化

// 设置来自父应用的消息监听器
function setupParentMessageListener() {
  if (typeof window === 'undefined') return

  window.addEventListener('message', (event: MessageEvent) => {
    // 这里可以添加origin验证，但为了简化先使用通配符
    const { type, payload } = event.data

    switch (type) {
      case 'PAYMENT_REDIRECT_ERROR':
        handlePaymentRedirectError(payload)
        break
      case 'RESTART_GAME':
        handleRestartGame(payload)
        break
      case 'STATE_SYNC':
        handleStateSync(payload)
        break
      // 可以在这里添加其他来自父应用的消息处理
      default:
        // 忽略未知消息类型
        break
    }
  })
}

// 处理支付重定向错误
function handlePaymentRedirectError(payload: any) {
  console.error('💳 CSR应用: 收到支付重定向错误', payload)

  // 动态导入Message组件来显示错误
  import('./mobile/components/Message')
    .then(({ Message }) => {
      Message.error(payload.error || 'Payment redirect failed')
    })
    .catch(() => {
      // 如果Message组件导入失败，使用alert作为降级
      alert(payload.error || 'Payment redirect failed')
    })
}

// 处理状态同步
function handleStateSync(payload: any) {
  // console.log('📥 CSR应用: 收到主应用状态同步', payload)

  // 存储页面状态供支付回调使用
  if (payload.page) {
    setMainAppPageState(payload.page)
  }
}

// 防重复执行的标志
let isRestartInProgress = false

// 处理游戏重启
function handleRestartGame(payload: any) {
  // 防重复执行检查
  if (isRestartInProgress) {
    return
  }

  isRestartInProgress = true

  // 动态导入store并设置restart状态
  import('./store/chat-events')
    .then(({ useChatEventsStore }) => {
      const chatEventsStore = useChatEventsStore()

      // 如果已经是restart状态，不重复设置
      if (chatEventsStore.isShouldRestart) {
        isRestartInProgress = false
        return
      }

      chatEventsStore.setShouldRestart(true)

      // 重置防重复标志
      setTimeout(() => {
        isRestartInProgress = false
      }, 2000) // 2秒后重置，防止卡死
    })
    .catch((error) => {
      console.error('CSR应用 设置restart状态失败:', error)
      isRestartInProgress = false
    })

  // 同时设置chat store的restart状态（如果存在）
  import('./store/chat')
    .then(({ useChatStore }) => {
      const chatStore = useChatStore()

      if (chatStore.setShouldRestart) {
        chatStore.setShouldRestart(true)
      }
    })
    .catch((error) => {
      console.error('CSR应用 设置chat store restart状态失败:', error)
    })
}

// 标记应用挂载完成（仅在开发环境）
// if (import.meta.env.DEV) {
//   console.log(`📱 应用挂载时间: ${performance.now().toFixed(2)}ms`)

//   // 在开发环境加载缓存测试工具
//   import('./utils/cache-test')
//     .then(({ CacheTestUtils }) => {
//       console.log('🛠️ 缓存测试工具已加载，可在控制台使用 CacheTestUtils')
//     })
//     .catch(console.warn)
// }

// 延迟加载非关键功能
async function initializeNonCriticalFeatures() {
  try {
    // 并行加载所有非关键模块
    // 直接在 Promise.all 中解构，避免冗余的二次解构
    const [
      { registerDirectives },
      { performanceMonitor },
      // { initSentry },
      { setupGlobalErrorHandlers },
      { initializeRoutePreload },
      { versionManager },
      { trackPWAMetrics },
      isMobileModule,
    ] = await Promise.all([
      import('./directives'),
      import('./utils/performance'),
      // import('./utils/sentry'),
      import('./utils/errorHandler'),
      import('./utils/route-preload-init'),
      import('./utils/version-manager'),
      import('./utils/pwa'),
      import('ismobilejs'),
    ])

    // 条件加载开发工具 - 生产环境中会被 Tree Shaking 移除
    const { safeInitEnvironment } = await import('./utils/dev-tools')

    // 加载非关键样式
    Promise.all([
      import('@/assets/style/accessibility.less'),
      import('@/assets/style/talent-guide.less'),
    ]).catch(console.warn)

    // 初始化环境
    safeInitEnvironment()

    // 注册指令
    registerDirectives(app)

    // 启动性能监控
    performanceMonitor.mark('app-start')
    performanceMonitor.mark('app-mounted')
    performanceMonitor.measure('app-initialization', 'app-start', 'app-mounted')

    // 初始化 Sentry (在生产环境)
    // if (['prod', 'prod.southeastAsia'].includes(import.meta.env.MODE)) {
    //   const sentryInitialized = initSentry(app, router)
    //   if (sentryInitialized) {
    //     console.log('Sentry 错误监控已启用')
    //     setupGlobalErrorHandlers()

    //     // 延迟发送真实的首屏性能指标
    //     setTimeout(() => {
    //       import('./utils/sentry')
    //         .then(({ reportAccuratePageLoadMetrics }) => {
    //           reportAccuratePageLoadMetrics()
    //         })
    //         .catch(console.warn)
    //     }, 2000) // 等待2秒确保FCP/LCP数据可用
    //   }
    // }

    // OpenReplay 录屏初始化已移至更晚的时机，减少对 LCP 的影响
    // 详见下方的 initializeOpenReplay 函数

    // 初始化路由预加载策略
    const isDeviceMobile = isMobileModule.default(navigator.userAgent).any
    initializeRoutePreload(isDeviceMobile)
      .then(() => {
        console.log('🚀 路由预加载策略已启动')
      })
      .catch((error: any) => {
        console.error('❌ 路由预加载策略启动失败:', error)
      })

    // 初始化PWA监控
    trackPWAMetrics()
    console.log('📱 PWA监控已启动')

    // 初始化版本管理器（仅在生产环境）
    if (['prod', 'prod.southeastAsia', 'pre'].includes(import.meta.env.MODE)) {
      console.log('🔄 启动版本管理器...')
      versionManager.getInstance()
      console.log('📦 版本管理器已启动')

      // 延迟版本检查
      setTimeout(() => {
        versionManager
          .checkForUpdates()
          .then(() => {
            console.log('✅ 首次版本检查完成')
          })
          .catch((error: any) => {
            console.warn('⚠️ 首次版本检查失败:', error)
          })
      }, 3000)
    }

    // Stripe 预加载已移至 shared-payment 包统一管理

    console.log('✅ 非关键功能初始化完成')
  } catch (error) {
    console.error('❌ 非关键功能初始化失败:', error)
  }
}

// 使用 requestIdleCallback 或 setTimeout 延迟初始化
if ('requestIdleCallback' in window) {
  requestIdleCallback(initializeNonCriticalFeatures, { timeout: 1000 })
} else {
  setTimeout(initializeNonCriticalFeatures, 100)
}

/**
 * 初始化 OpenReplay 录屏 - 进一步延迟以减少对 LCP 的影响
 */
async function initializeOpenReplay() {
  try {
    // 只在生产环境启用
    if (!['prod.southeastAsia'].includes(import.meta.env.MODE)) {
      return
    }

    console.log('🎬 开始初始化 OpenReplay 录屏...')

    // 动态导入 OpenReplay Tracker
    const [{ getDeviceId }, { default: Tracker }] = await Promise.all([
      import('./utils/util'),
      import('@openreplay/tracker'),
    ])

    const deviceId = getDeviceId()
    const tracker = new Tracker({
      projectKey: import.meta.env.VITE_OPENREPLAY_PROJECT_KEY,
      ingestPoint: import.meta.env.VITE_OPENREPLAY_INGEST_POINT,
    })

    tracker.setUserAnonymousID(deviceId)

    // 将 tracker 挂载到全局，供其他地方使用
    ;(window as any).openReplayTracker = tracker

    tracker?.start()
    console.log('✅ OpenReplay 录屏初始化完成')
  } catch (error) {
    console.warn('⚠️ OpenReplay 录屏初始化失败:', error)
  }
}

// 延迟初始化 OpenReplay - 用户交互后或5秒后
let openReplayInitialized = false

function initOpenReplayOnInteraction() {
  if (openReplayInitialized) return
  openReplayInitialized = true

  // 再延迟2秒，确保不影响用户交互响应
  setTimeout(initializeOpenReplay, 2000)
}

// 监听用户首次交互
const interactionEvents = [
  'mousedown',
  'mousemove',
  'keypress',
  'scroll',
  'touchstart',
  'click',
]
interactionEvents.forEach((event) => {
  document.addEventListener(event, initOpenReplayOnInteraction, {
    once: true,
    passive: true,
  })
})

// 备用方案：5秒后自动初始化
setTimeout(() => {
  if (!openReplayInitialized) {
    initOpenReplayOnInteraction()
  }
}, 5000)

// 在微前端环境中隐藏HTML loading并报告进度
setTimeout(() => {
  try {
    // 动态导入避免在非微前端环境中出错
    import('./utils/iframeNavigation')
      .then(({ notifyAppReady, reportLoadingProgress, isInMicroFrontend }) => {
        if (isInMicroFrontend()) {
          // 立即隐藏HTML loading
          const htmlLoading = document.getElementById('loading-indicator')
          if (htmlLoading) {
            htmlLoading.style.display = 'none'
          }

          // 报告应用初始化进度
          reportLoadingProgress('app-init', 30)

          // 通知应用就绪
          notifyAppReady()
        }
      })
      .catch(() => {
        // 忽略导入错误，可能在非微前端环境中
      })
  } catch (error) {
    // 忽略错误，可能在非微前端环境中
  }
}, 200) // 缩短延迟，尽快隐藏HTML loading
